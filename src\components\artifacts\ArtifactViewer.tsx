import React from 'react'
import { Artifact } from '../../types'
import { ImageArtifactViewer } from './viewers/ImageArtifactViewer'
import { CodeArtifactViewer } from './viewers/CodeArtifactViewer'
import { MarkdownArtifactViewer } from './viewers/MarkdownArtifactViewer'
import { MermaidArtifactViewer } from './viewers/MermaidArtifactViewer'
import { HtmlArtifactViewer } from './viewers/HtmlArtifactViewer'

interface ArtifactViewerProps {
  artifact: Artifact
}

export function ArtifactViewer({ artifact }: ArtifactViewerProps) {
  const [isLoading, setIsLoading] = React.useState(true)
  const [error, setError] = React.useState<string | null>(null)

  React.useEffect(() => {
    // Simulate loading for complex artifacts
    const timer = setTimeout(() => {
      setIsLoading(false)
    }, 100)

    return () => clearTimeout(timer)
  }, [artifact.id])

  const renderArtifact = () => {
    if (isLoading) {
      return (
        <div className="flex items-center justify-center h-full">
          <div className="text-center text-neutral-400">
            <div className="animate-spin w-8 h-8 border-2 border-indigo-500 border-t-transparent rounded-full mx-auto mb-2"></div>
            <div>Loading artifact...</div>
          </div>
        </div>
      )
    }

    if (error) {
      return (
        <div className="flex items-center justify-center h-full text-red-400">
          <div className="text-center">
            <div className="text-4xl mb-4">⚠️</div>
            <div className="text-lg font-medium mb-2">Error Loading Artifact</div>
            <div className="text-sm">{error}</div>
          </div>
        </div>
      )
    }
    switch (artifact.type) {
      case 'image':
        return <ImageArtifactViewer artifact={artifact} />
      
      case 'code':
      case 'json':
        return <CodeArtifactViewer artifact={artifact} />
      
      case 'markdown':
        return <MarkdownArtifactViewer artifact={artifact} />
      
      case 'mermaid':
        return <MermaidArtifactViewer artifact={artifact} />
      
      case 'html':
        return <HtmlArtifactViewer artifact={artifact} />
      
      default:
        return (
          <div className="flex items-center justify-center h-full text-neutral-400">
            <div className="text-center">
              <div className="text-4xl mb-4">❓</div>
              <div className="text-lg font-medium mb-2">Unsupported Artifact Type</div>
              <div className="text-sm">
                Type: {artifact.type}
              </div>
            </div>
          </div>
        )
    }
  }

  return (
    <div className="h-full flex flex-col">
      {/* Artifact metadata */}
      <div className="flex-shrink-0 p-4 border-b border-tertiary bg-gray-800/50">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="font-medium text-supplement1">{artifact.title}</h3>
            <div className="text-sm text-gray-400 mt-1">
              {artifact.type.toUpperCase()}
              {artifact.metadata.language && ` • ${artifact.metadata.language}`}
              {artifact.metadata.size && ` • ${formatFileSize(artifact.metadata.size)}`}
            </div>
          </div>
          <div className="text-xs text-gray-500">
            {formatDate(artifact.metadata.createdAt)}
          </div>
        </div>
      </div>

      {/* Artifact content */}
      <div className="flex-1 overflow-hidden">
        {renderArtifact()}
      </div>
    </div>
  )
}

// Helper functions
function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

function formatDate(dateString: string): string {
  const date = new Date(dateString)
  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffMins = Math.floor(diffMs / 60000)
  const diffHours = Math.floor(diffMs / 3600000)
  const diffDays = Math.floor(diffMs / 86400000)

  if (diffMins < 1) return 'Just now'
  if (diffMins < 60) return `${diffMins}m ago`
  if (diffHours < 24) return `${diffHours}h ago`
  if (diffDays < 7) return `${diffDays}d ago`
  
  return date.toLocaleDateString()
}
